.post {
  padding: 1rem;
}

.postHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.postUser {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.userAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.userAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.postUserInfo h4 {
  margin: 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
}

.postMeta {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.postMeta i {
  font-size: 0.75rem;
}

.postMenu {
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.postMenu:hover {
  background: var(--hover-background);
}

.postContent {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: var(--text-primary);
  white-space: pre-wrap;
  word-wrap: break-word;
}

.postImage {
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
  max-height: 400px;
}

.postImage img {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
}

.postStats {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.postActionsRow {
  display: flex;
  gap: 1rem;
}

.postAction {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
  border: none;
  background: transparent;
}

.postAction:hover {
  background: var(--hover-background);
  color: var(--primary-color);
}

.postAction i {
  font-size: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .post {
    padding: 0.75rem;
  }
  
  .postUser {
    gap: 0.5rem;
  }
  
  .userAvatar {
    width: 36px;
    height: 36px;
  }
  
  .postUserInfo h4 {
    font-size: 0.9rem;
  }
  
  .postMeta {
    font-size: 0.75rem;
  }
  
  .postContent {
    font-size: 0.95rem;
  }
  
  .postActionsRow {
    gap: 0.5rem;
  }
  
  .postAction {
    padding: 0.5rem;
    font-size: 0.85rem;
  }
}
